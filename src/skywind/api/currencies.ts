import { ValidationError } from "../errors";
import type { FastifyInstanceType } from "../fastify";
import {
    createCurrency,
    deleteCurrency,
    findCurrencyByCode,
    getAllCurrencies,
    updateCurrency
} from "../models/currency";
import { verifyToken } from "../token";

export default function (router: FastifyInstanceType, _options: any, done: () => void) {
    router.get("/currencies",
        {
            schema: {
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" }
                    },
                    required: ["token"]
                }
            } as const
        },
        async (req, res) => {
            await verifyToken(req.query.token);
            const currencies = await getAllCurrencies();
            return res.send(currencies);
        });

    router.get("/currencies/:code",
        {
            schema: {
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" }
                    },
                    required: ["token"]
                },
                params: {
                    type: "object",
                    properties: {
                        code: { type: "string" }
                    },
                    required: ["code"]
                }
            } as const
        },
        async (req, res) => {
            await verifyToken(req.query.token);
            const currency = await findCurrencyByCode(req.params.code);
            if (!currency) {
                return res.status(404).send({ error: `Currency with code ${req.params.code} not found` });
            }
            return res.send(currency);
        });

    router.post("/currencies",
        {
            schema: {
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" }
                    },
                    required: ["token"]
                },
                body: {
                    type: "object",
                    properties: {
                        code: { type: "string" },
                        name: { type: "string" },
                        iso: {
                            type: "object",
                            properties: {
                                code: { type: "string" },
                                number: { type: "string" },
                                minorUnits: { type: "integer" }
                            },
                            required: ["code", "number", "minorUnits"]
                        },
                        isVirtual: { type: "boolean" },
                        isSocial: { type: "boolean" },
                        disableGGR: { type: "boolean" },
                        originCurrency: {
                            type: "object",
                            properties: {
                                currency: { type: "string" },
                                multiplier: { type: "number" }
                            },
                            required: ["currency", "multiplier"]
                        }
                    },
                    required: ["code", "name", "iso"]
                }
            }
        },
        async (req, res) => {
            await verifyToken(req.query.token);
            const existingCurrency = await findCurrencyByCode(req.body.code);
            if (existingCurrency) {
                return Promise.reject(new ValidationError(`Currency with code ${req.body.code} already exists`));
            }
            const currency = await createCurrency(req.body);
            return res.status(201).send(currency);
        });

    router.patch("/currencies/:code",
        {
            schema: {
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" }
                    },
                    required: ["token"]
                },
                params: {
                    type: "object",
                    properties: {
                        code: { type: "string" }
                    },
                    required: ["code"]
                },
                body: {
                    type: "object",
                    properties: {
                        code: { type: "string" },
                        name: { type: "string" },
                        iso: {
                            type: "object",
                            properties: {
                                code: { type: "string" },
                                number: { type: "string" },
                                minorUnits: { type: "integer" }
                            }
                        },
                        isVirtual: { type: "boolean" },
                        isSocial: { type: "boolean" },
                        disableGGR: { type: "boolean" },
                        originCurrency: {
                            type: "object",
                            properties: {
                                currency: { type: "string" },
                                multiplier: { type: "number" }
                            }
                        }
                    }
                }
            } as const
        },
        async (req, res) => {
            await verifyToken(req.query.token);
            const currency = await updateCurrency(req.params.code, req.body);
            if (!currency) {
                return res.status(404).send({ error: `Currency with code ${req.params.code} not found` });
            }
            return res.send(currency);
        });

    router.delete("/currencies/:code",
        {
            schema: {
                querystring: {
                    type: "object",
                    properties: {
                        token: { type: "string" }
                    },
                    required: ["token"]
                },
                params: {
                    type: "object",
                    properties: {
                        code: { type: "string" }
                    },
                    required: ["code"]
                }
            }
        },
        async (req, res) => {
            await verifyToken(req.query.token);
            const deleted = await deleteCurrency(req.params.code);
            if (!deleted) {
                return res.status(404).send({ error: `Currency with code ${req.params.code} not found` });
            }
            return res.status(204).send();
        });

    done();
}
