import * as Sequelize from "sequelize";
import { sequelize as db } from "./db";
import { Currency } from "@skywind-group/sw-currency-exchange";
import { ValidationError } from "../errors";
import { merge } from "lodash";

/**
 * Makes all properties in T optional and recursively applies the same to all nested objects
 */
export type DeepPartial<T> = T extends object ? {
    [P in keyof T]?: DeepPartial<T[P]>;
} : T;

export type CurrencyType = Sequelize.Optional<Omit<Currency, "multiplier" | "exponent" | "clientMultiplier" | "toMinorUnits" | "toMajorUnits" | "format" | "toFixedByExponent">, "isVirtual" | "isSocial" | "iso" | "toEURMultiplier" | "copyLimitsFrom" | "provider" | "clientMoneyFormat" | "disableGGR" | "originCurrency">;

interface CurrencyAttributes {
    code: string;
    name: string;
    properties: {
        iso?: Currency["iso"];
        toEURMultiplier?: number;
        copyLimitsFrom?: string;
        provider?: string;
        clientMinorUnits?: number;
        clientMoneyFormat?: Currency["clientMoneyFormat"];
    };
    type?: "virtual" | "social" | null;
    disableGGR?: boolean | null;
    originCurrencyCode?: string | null;
    originCurrencyMultiplier?: number | null;
}

interface CurrencyDBInstance extends Sequelize.Model<
    Sequelize.InferAttributes<CurrencyDBInstance>,
    Sequelize.InferCreationAttributes<CurrencyDBInstance>
>, CurrencyAttributes {
}

type CurrencyModel = Sequelize.ModelStatic<CurrencyDBInstance>;

const currencyModel: CurrencyModel = db.define<CurrencyDBInstance, CurrencyAttributes>(
    "currencies",
    {
        code: {
            type: Sequelize.DataTypes.CHAR(3),
            allowNull: false,
            primaryKey: true
        },
        name: {
            type: Sequelize.DataTypes.STRING(100),
            allowNull: false
        },
        properties: {
            field: "properties",
            type: Sequelize.DataTypes.JSONB,
            allowNull: true
        },
        type: {
            type: Sequelize.DataTypes.ENUM("virtual", "social"),
            allowNull: true
        },
        disableGGR: {
            field: "disable_ggr",
            type: Sequelize.DataTypes.BOOLEAN,
            allowNull: true,
            defaultValue: false
        },
        originCurrencyCode: {
            field: "origin_currency_code",
            type: Sequelize.DataTypes.CHAR(3),
            allowNull: true
        },
        originCurrencyMultiplier: {
            field: "origin_currency_multiplier",
            type: Sequelize.DataTypes.DECIMAL,
            allowNull: true
        }
    },
    {
        tableName: "currencies",
        timestamps: true,
        underscored: true
    }
);

export function getCurrencyModel() {
    return currencyModel;
}

export async function findCurrencyByCode(code: string): Promise<Currency | null> {
    const item = await currencyModel.findByPk(code);
    return item ? toCurrency(item) : null;
}

export async function getAllCurrencies(): Promise<Currency[]> {
    const items = await currencyModel.findAll();
    return items.map(toCurrency);
}

export async function createCurrency(data: CurrencyType) {
    const currency = await currencyModel.create(toCurrencyAttributes(data));
    return toCurrency(currency);
}

export async function updateCurrency(code: string, data: DeepPartial<CurrencyType>) {
    const currency = await findCurrencyByCode(code);
    if (!currency) {
        throw new ValidationError("Currency not found");
    }
    const [updatedCount] = await currencyModel.update(toCurrencyAttributes(merge(data, currency)), {
        where: { code }
    });
    if (updatedCount === 0) {
        return null;
    }
    return findCurrencyByCode(code);
}

export async function deleteCurrency(code: string): Promise<boolean> {
    const deleted = await currencyModel.destroy({
        where: { code }
    });
    return deleted > 0;
}

function toCurrency(record: CurrencyDBInstance): Currency {
    return new Currency(
        record.code,
        record.name,
        {
            code: record.properties.iso.code,
            number: record.properties.iso.number,
            minorUnits: record.properties.iso.minorUnits
        },
        record.type,
        record.properties.toEURMultiplier,
        record.properties.copyLimitsFrom,
        record.properties.provider,
        (record.originCurrencyCode && record.originCurrencyMultiplier !== null) ? {
            currency: record.originCurrencyCode,
            multiplier: record.originCurrencyMultiplier
        } : undefined,
        record.properties.clientMinorUnits,
        record.properties.clientMoneyFormat,
        record.disableGGR
    );
}

function toCurrencyAttributes({ code, name, isVirtual, isSocial, originCurrency, ...properties }: CurrencyType): Partial<CurrencyAttributes> {
    return {
        code,
        name,
        properties,
        type: isVirtual ? "virtual" : isSocial ? "social" : undefined,
        originCurrencyCode: originCurrency?.currency,
        originCurrencyMultiplier: originCurrency?.multiplier
    };
}
