import { expect } from "chai";
import * as sinon from "sinon";
import type { FastifyInstance } from "fastify";
import { create } from "../../skywind/fastify";
import currenciesApi from "../../skywind/api/currencies";
import * as currencyModel from "../../skywind/models/currency";
import * as token from "../../skywind/token";

describe("Currencies API", () => {
    let app: FastifyInstance;
    let verifyTokenStub;
    let findCurrencyByCodeStub;
    let getAllCurrenciesStub;
    let createCurrencyStub;
    let updateCurrencyStub;
    let deleteCurrencyStub;

    beforeEach(async () => {
        app = create();
        app.register(currenciesApi, { prefix: "/v1" });
        await app.ready();

        verifyTokenStub = sinon.stub(token, "verifyToken").resolves();
        findCurrencyByCodeStub = sinon.stub(currencyModel, "findCurrencyByCode");
        getAllCurrenciesStub = sinon.stub(currencyModel, "getAllCurrencies");
        createCurrencyStub = sinon.stub(currencyModel, "createCurrency");
        updateCurrencyStub = sinon.stub(currencyModel, "updateCurrency");
        deleteCurrencyStub = sinon.stub(currencyModel, "deleteCurrency");
    });

    afterEach(() => {
        sinon.restore();
    });

    describe("GET /v1/currencies", () => {
        it("should return all currencies", async () => {
            const mockCurrencies = [
                {
                    code: "USD",
                    name: "US Dollar",
                    iso: {
                        code: "USD",
                        number: "840",
                        minorUnits: 2
                    },
                    isVirtual: false,
                    isSocial: false,
                    disableGGR: false
                },
                {
                    code: "EUR",
                    name: "Euro",
                    iso: {
                        code: "EUR",
                        number: "978",
                        minorUnits: 2
                    },
                    isVirtual: false,
                    isSocial: false,
                    disableGGR: false
                }
            ];

            getAllCurrenciesStub.resolves(mockCurrencies);

            const response = await app.inject({
                method: "GET",
                url: "/v1/currencies?token=valid-token"
            });

            expect(response.statusCode).to.equal(200);
            expect(JSON.parse(response.payload)).to.deep.equal(mockCurrencies);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(getAllCurrenciesStub.calledOnce).to.be.true;
        });
    });

    describe("GET /v1/currencies/:code", () => {
        it("should return a currency when found", async () => {
            const mockCurrency = {
                code: "USD",
                name: "US Dollar",
                iso: {
                    code: "USD",
                    number: "840",
                    minorUnits: 2
                },
                isVirtual: false,
                isSocial: false,
                disableGGR: false
            };

            findCurrencyByCodeStub.resolves(mockCurrency);

            const response = await app.inject({
                method: "GET",
                url: "/v1/currencies/USD?token=valid-token"
            });

            expect(response.statusCode).to.equal(200);
            expect(JSON.parse(response.payload)).to.deep.equal(mockCurrency);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(findCurrencyByCodeStub.calledOnceWith("USD")).to.be.true;
        });

        it("should return 404 when currency not found", async () => {
            findCurrencyByCodeStub.resolves(null);

            const response = await app.inject({
                method: "GET",
                url: "/v1/currencies/XYZ?token=valid-token"
            });

            expect(response.statusCode).to.equal(404);
            expect(JSON.parse(response.payload)).to.deep.equal({ error: "Currency with code XYZ not found" });
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(findCurrencyByCodeStub.calledOnceWith("XYZ")).to.be.true;
        });
    });

    describe("POST /v1/currencies", () => {
        it("should create a virtual currency", async () => {
            const currencyData = {
                code: "BTC",
                name: "Bitcoin",
                iso: {
                    code: "BTC",
                    number: "999",
                    minorUnits: 8
                },
                isVirtual: true,
                isSocial: false,
                disableGGR: false
            };

            findCurrencyByCodeStub.resolves(null);
            createCurrencyStub.resolves(currencyData);

            const response = await app.inject({
                method: "POST",
                url: "/v1/currencies?token=valid-token",
                payload: currencyData
            });

            expect(response.statusCode).to.equal(201);
            expect(JSON.parse(response.payload)).to.deep.equal(currencyData);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(findCurrencyByCodeStub.calledOnceWith("BTC")).to.be.true;
            expect(createCurrencyStub.calledOnceWith(currencyData)).to.be.true;
        });

        it("should create a social currency with origin currency", async () => {
            const currencyData = {
                code: "GOLD",
                name: "Gold Coins",
                iso: {
                    code: "GOLD",
                    number: "998",
                    minorUnits: 0
                },
                isVirtual: false,
                isSocial: true,
                disableGGR: true,
                originCurrency: {
                    currency: "USD",
                    multiplier: 0.01
                }
            };

            findCurrencyByCodeStub.resolves(null);
            createCurrencyStub.resolves(currencyData);

            const response = await app.inject({
                method: "POST",
                url: "/v1/currencies?token=valid-token",
                payload: currencyData
            });

            expect(response.statusCode).to.equal(201);
            expect(JSON.parse(response.payload)).to.deep.equal(currencyData);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(findCurrencyByCodeStub.calledOnceWith("GOLD")).to.be.true;
            expect(createCurrencyStub.calledOnceWith(currencyData)).to.be.true;
        });

        it("should return error when currency already exists", async () => {
            const currencyData = {
                code: "USD",
                name: "US Dollar",
                iso: {
                    code: "USD",
                    number: "840",
                    minorUnits: 2
                }
            };

            const existingCurrency = {
                code: "USD",
                name: "US Dollar",
                iso: {
                    code: "USD",
                    number: "840",
                    minorUnits: 2
                },
                isVirtual: false,
                isSocial: false,
                disableGGR: false
            };

            findCurrencyByCodeStub.resolves(existingCurrency);

            const response = await app.inject({
                method: "POST",
                url: "/v1/currencies?token=valid-token",
                payload: currencyData
            });

            expect(response.statusCode).to.equal(400);
            expect(JSON.parse(response.payload)).to.deep.include({
                message: "Validation error: Currency with code USD already exists"
            });
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(findCurrencyByCodeStub.calledOnceWith("USD")).to.be.true;
            expect(createCurrencyStub.called).to.be.false;
        });

        it("should validate required fields", async () => {
            const invalidCurrencyData = {
                code: "TEST",
                name: "Test Currency"
            };

            const response = await app.inject({
                method: "POST",
                url: "/v1/currencies?token=valid-token",
                payload: invalidCurrencyData
            });

            expect(response.statusCode).to.equal(400);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(createCurrencyStub.called).to.be.false;
        });

        it("should validate iso object structure", async () => {
            const invalidCurrencyData = {
                code: "TEST",
                name: "Test Currency",
                iso: {
                    code: "TEST"
                }
            };

            const response = await app.inject({
                method: "POST",
                url: "/v1/currencies?token=valid-token",
                payload: invalidCurrencyData
            });

            expect(response.statusCode).to.equal(400);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(createCurrencyStub.called).to.be.false;
        });
    });

    describe("PATCH /v1/currencies/:code", () => {
        it("should update a currency name", async () => {
            const code = "USD";
            const updateData = {
                name: "Updated US Dollar"
            };

            const updatedCurrency = {
                code: "USD",
                name: "Updated US Dollar",
                iso: {
                    code: "USD",
                    number: "840",
                    minorUnits: 2
                },
                isVirtual: false,
                isSocial: false,
                disableGGR: false
            };

            updateCurrencyStub.resolves(updatedCurrency);

            const response = await app.inject({
                method: "PATCH",
                url: `/v1/currencies/${code}?token=valid-token`,
                payload: updateData
            });

            expect(response.statusCode).to.equal(200);
            expect(JSON.parse(response.payload)).to.deep.equal(updatedCurrency);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(updateCurrencyStub.calledOnceWith(code, updateData)).to.be.true;
        });

        it("should update currency properties", async () => {
            const code = "BTC";
            const updateData = {
                disableGGR: true,
                originCurrency: {
                    currency: "EUR",
                    multiplier: 0.85
                }
            };

            const updatedCurrency = {
                code: "BTC",
                name: "Bitcoin",
                iso: {
                    code: "BTC",
                    number: "999",
                    minorUnits: 8
                },
                isVirtual: true,
                isSocial: false,
                disableGGR: true,
                originCurrency: {
                    currency: "EUR",
                    multiplier: 0.85
                }
            };

            updateCurrencyStub.resolves(updatedCurrency);

            const response = await app.inject({
                method: "PATCH",
                url: `/v1/currencies/${code}?token=valid-token`,
                payload: updateData
            });

            expect(response.statusCode).to.equal(200);
            expect(JSON.parse(response.payload)).to.deep.equal(updatedCurrency);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(updateCurrencyStub.calledOnceWith(code, updateData)).to.be.true;
        });

        it("should return 404 when currency not found", async () => {
            const code = "XYZ";
            const updateData = {
                name: "Non-existent Currency"
            };

            updateCurrencyStub.resolves(null);

            const response = await app.inject({
                method: "PATCH",
                url: `/v1/currencies/${code}?token=valid-token`,
                payload: updateData
            });

            expect(response.statusCode).to.equal(404);
            expect(JSON.parse(response.payload)).to.deep.equal({ error: `Currency with code ${code} not found` });
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(updateCurrencyStub.calledOnceWith(code, updateData)).to.be.true;
        });
    });

    describe("DELETE /v1/currencies/:code", () => {
        it("should delete a currency", async () => {
            const code = "USD";
            deleteCurrencyStub.resolves(true);

            const response = await app.inject({
                method: "DELETE",
                url: `/v1/currencies/${code}?token=valid-token`
            });

            expect(response.statusCode).to.equal(204);
            expect(response.payload).to.equal("");
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(deleteCurrencyStub.calledOnceWith(code)).to.be.true;
        });

        it("should return 404 when currency not found", async () => {
            const code = "XYZ";
            deleteCurrencyStub.resolves(false);

            const response = await app.inject({
                method: "DELETE",
                url: `/v1/currencies/${code}?token=valid-token`
            });

            expect(response.statusCode).to.equal(404);
            expect(JSON.parse(response.payload)).to.deep.equal({ error: `Currency with code ${code} not found` });
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(deleteCurrencyStub.calledOnceWith(code)).to.be.true;
        });
    });
});
