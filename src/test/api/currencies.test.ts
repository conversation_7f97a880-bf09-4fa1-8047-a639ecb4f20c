import { expect } from "chai";
import * as sinon from "sinon";
import type { FastifyInstance } from "fastify";
import { create } from "../../skywind/fastify";
import currenciesApi from "../../skywind/api/currencies";
import * as currencyModel from "../../skywind/models/currency";
import * as token from "../../skywind/token";

describe("Currencies API", () => {
    let app: FastifyInstance;
    let verifyTokenStub;
    let findCurrencyByCodeStub;
    let getAllCurrenciesStub;
    let createCurrencyStub;
    let updateCurrencyStub;
    let deleteCurrencyStub;

    beforeEach(async () => {
        app = create();
        app.register(currenciesApi, { prefix: "/v1" });
        await app.ready();

        verifyTokenStub = sinon.stub(token, "verifyToken").resolves();
        findCurrencyByCodeStub = sinon.stub(currencyModel, "findCurrencyByCode");
        getAllCurrenciesStub = sinon.stub(currencyModel, "getAllCurrencies");
        createCurrencyStub = sinon.stub(currencyModel, "createCurrency");
        updateCurrencyStub = sinon.stub(currencyModel, "updateCurrency");
        deleteCurrencyStub = sinon.stub(currencyModel, "deleteCurrency");
    });

    afterEach(() => {
        sinon.restore();
    });

    describe("GET /v1/currencies", () => {
        it("should return all currencies", async () => {
            const mockCurrencies = [
                {
                    code: "USD",
                    name: "US Dollar",
                    isoCode: "USD",
                    isoNumber: "840",
                    isoMinorUnits: 2
                },
                {
                    code: "EUR",
                    name: "Euro",
                    isoCode: "EUR",
                    isoNumber: "978",
                    isoMinorUnits: 2
                }
            ];

            getAllCurrenciesStub.resolves(mockCurrencies);

            const response = await app.inject({
                method: "GET",
                url: "/v1/currencies?token=valid-token"
            });

            expect(response.statusCode).to.equal(200);
            expect(JSON.parse(response.payload)).to.deep.equal(mockCurrencies);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(getAllCurrenciesStub.calledOnce).to.be.true;
        });
    });

    describe("GET /v1/currencies/:code", () => {
        it("should return a currency when found", async () => {
            const mockCurrency = {
                code: "USD",
                name: "US Dollar",
                isoCode: "USD",
                isoNumber: "840",
                isoMinorUnits: 2
            };

            findCurrencyByCodeStub.resolves(mockCurrency);

            const response = await app.inject({
                method: "GET",
                url: "/v1/currencies/USD?token=valid-token"
            });

            expect(response.statusCode).to.equal(200);
            expect(JSON.parse(response.payload)).to.deep.equal(mockCurrency);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(findCurrencyByCodeStub.calledOnceWith("USD")).to.be.true;
        });

        it("should return 404 when currency not found", async () => {
            findCurrencyByCodeStub.resolves(null);

            const response = await app.inject({
                method: "GET",
                url: "/v1/currencies/XYZ?token=valid-token"
            });

            expect(response.statusCode).to.equal(404);
            expect(JSON.parse(response.payload)).to.deep.equal({ error: "Currency with code XYZ not found" });
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(findCurrencyByCodeStub.calledOnceWith("XYZ")).to.be.true;
        });
    });

    describe("POST /v1/currencies", () => {
        it("should create a currency", async () => {
            const currencyData = {
                code: "BTC",
                name: "Bitcoin",
                isoCode: "BTC",
                isoNumber: "999",
                isoMinorUnits: 8
            };

            findCurrencyByCodeStub.resolves(null);
            createCurrencyStub.resolves(currencyData);

            const response = await app.inject({
                method: "POST",
                url: "/v1/currencies?token=valid-token",
                payload: currencyData
            });

            expect(response.statusCode).to.equal(201);
            expect(JSON.parse(response.payload)).to.deep.equal(currencyData);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(findCurrencyByCodeStub.calledOnceWith("BTC")).to.be.true;
            expect(createCurrencyStub.calledOnceWith(currencyData)).to.be.true;
        });

        it("should return error when currency already exists", async () => {
            const currencyData = {
                code: "USD",
                name: "US Dollar",
                isoCode: "USD",
                isoNumber: "840",
                isoMinorUnits: 2
            };

            findCurrencyByCodeStub.resolves(currencyData);

            const response = await app.inject({
                method: "POST",
                url: "/v1/currencies?token=valid-token",
                payload: currencyData
            });

            expect(response.statusCode).to.equal(400);
            expect(JSON.parse(response.payload)).to.deep.include({ 
                message: "Currency with code USD already exists" 
            });
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(findCurrencyByCodeStub.calledOnceWith("USD")).to.be.true;
            expect(createCurrencyStub.called).to.be.false;
        });
    });

    describe("PUT /v1/currencies/:code", () => {
        it("should update a currency", async () => {
            const code = "USD";
            const updateData = {
                name: "Updated US Dollar"
            };

            const updatedCurrency = {
                code: "USD",
                name: "Updated US Dollar",
                isoCode: "USD",
                isoNumber: "840",
                isoMinorUnits: 2
            };

            updateCurrencyStub.resolves(updatedCurrency);

            const response = await app.inject({
                method: "PUT",
                url: `/v1/currencies/${code}?token=valid-token`,
                payload: updateData
            });

            expect(response.statusCode).to.equal(200);
            expect(JSON.parse(response.payload)).to.deep.equal(updatedCurrency);
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(updateCurrencyStub.calledOnceWith(code, updateData)).to.be.true;
        });

        it("should return 404 when currency not found", async () => {
            const code = "XYZ";
            const updateData = {
                name: "Non-existent Currency"
            };

            updateCurrencyStub.resolves(null);

            const response = await app.inject({
                method: "PUT",
                url: `/v1/currencies/${code}?token=valid-token`,
                payload: updateData
            });

            expect(response.statusCode).to.equal(404);
            expect(JSON.parse(response.payload)).to.deep.equal({ error: `Currency with code ${code} not found` });
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(updateCurrencyStub.calledOnceWith(code, updateData)).to.be.true;
        });
    });

    describe("DELETE /v1/currencies/:code", () => {
        it("should delete a currency", async () => {
            const code = "USD";
            deleteCurrencyStub.resolves(true);

            const response = await app.inject({
                method: "DELETE",
                url: `/v1/currencies/${code}?token=valid-token`
            });

            expect(response.statusCode).to.equal(204);
            expect(response.payload).to.equal("");
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(deleteCurrencyStub.calledOnceWith(code)).to.be.true;
        });

        it("should return 404 when currency not found", async () => {
            const code = "XYZ";
            deleteCurrencyStub.resolves(false);

            const response = await app.inject({
                method: "DELETE",
                url: `/v1/currencies/${code}?token=valid-token`
            });

            expect(response.statusCode).to.equal(404);
            expect(JSON.parse(response.payload)).to.deep.equal({ error: `Currency with code ${code} not found` });
            expect(verifyTokenStub.calledOnceWith("valid-token")).to.be.true;
            expect(deleteCurrencyStub.calledOnceWith(code)).to.be.true;
        });
    });
});
